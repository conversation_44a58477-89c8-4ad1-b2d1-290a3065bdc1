<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL 3D 地球模型</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            user-select: none;
        }
        
        canvas {
            display: block;
            cursor: grab;
        }
        
        canvas:active {
            cursor: grabbing;
        }
        
        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .controls-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            pointer-events: all;
        }
        
        .control-item {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .control-item label {
            min-width: 80px;
        }
        
        .control-item input[type="range"] {
            flex: 1;
        }
        
        .control-item input[type="checkbox"] {
            transform: scale(1.2);
        }
        
        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            max-width: 300px;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            text-align: center;
        }
        
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <div>正在加载地球纹理...</div>
    </div>
    
    <canvas id="earthCanvas"></canvas>
    
    <div class="ui-overlay">
        <div class="controls-panel">
            <h3 style="margin-top: 0;">地球控制面板</h3>
            <div class="control-item">
                <label>自转速度:</label>
                <input type="range" id="rotationSpeed" min="0" max="2" step="0.1" value="0.5">
            </div>
            <div class="control-item">
                <label>缩放:</label>
                <input type="range" id="zoomLevel" min="2" max="10" step="0.1" value="5">
            </div>
            <div class="control-item">
                <label>显示云层:</label>
                <input type="checkbox" id="showClouds" checked>
            </div>
            <div class="control-item">
                <label>显示大气:</label>
                <input type="checkbox" id="showAtmosphere" checked>
            </div>
            <div class="control-item">
                <label>夜晚模式:</label>
                <input type="checkbox" id="nightMode">
            </div>
            <div class="control-item">
                <label>显示月亮:</label>
                <input type="checkbox" id="showMoon" checked>
            </div>
            <div class="control-item">
                <label>月亮轨道速度:</label>
                <input type="range" id="moonOrbitSpeed" min="0" max="3" step="0.1" value="1">
            </div>
            <div class="control-item">
                <label>显示轨道:</label>
                <input type="checkbox" id="showOrbit">
            </div>
        </div>
        
        <div class="info-panel">
            <h4 style="margin-top: 0;">操作说明</h4>
            <p>🖱️ <strong>鼠标拖拽:</strong> 旋转地球和月亮系统</p>
            <p>🔍 <strong>滚轮:</strong> 缩放视角</p>
            <p>⚙️ <strong>控制面板:</strong> 调整各种效果</p>
            <p>🌍 <strong>地球特性:</strong> 真实纹理、动态光照、大气效果</p>
            <p>🌙 <strong>月亮特性:</strong> 椭圆轨道、潮汐锁定、真实比例</p>
        </div>
    </div>

    <script>
        // 全局变量
        let scene, camera, renderer, earth, clouds, atmosphere, moon;
        let earthGroup, cloudGroup, moonGroup;
        let mouse = { x: 0, y: 0 };
        let isMouseDown = false;
        let rotationSpeed = 0.005;
        let autoRotate = true;
        let moonOrbitAngle = 0;
        
        // 纹理加载器
        const textureLoader = new THREE.TextureLoader();
        const loadingElement = document.getElementById('loading');
        
        // 初始化场景
        function initScene() {
            // 创建场景
            scene = new THREE.Scene();

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('earthCanvas'),
                antialias: true,
                alpha: true,
                powerPreference: "high-performance"
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1.0;

            // 创建动态星空背景
            createStarField();
        }

        // 创建星空背景
        function createStarField() {
            const starGeometry = new THREE.BufferGeometry();
            const starVertices = [];
            const starColors = [];

            for (let i = 0; i < 15000; i++) {
                // 随机位置
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starVertices.push(x, y, z);

                // 随机颜色（模拟不同类型的恒星）
                const starType = Math.random();
                if (starType < 0.7) {
                    // 白色恒星
                    starColors.push(1, 1, 1);
                } else if (starType < 0.85) {
                    // 蓝色恒星
                    starColors.push(0.7, 0.8, 1);
                } else if (starType < 0.95) {
                    // 红色恒星
                    starColors.push(1, 0.7, 0.6);
                } else {
                    // 黄色恒星
                    starColors.push(1, 1, 0.7);
                }
            }

            starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
            starGeometry.setAttribute('color', new THREE.Float32BufferAttribute(starColors, 3));

            const starMaterial = new THREE.PointsMaterial({
                size: 1.5,
                vertexColors: true,
                transparent: true,
                opacity: 0.8
            });

            const stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);

            // 添加银河系背景
            createGalaxyBackground();
        }

        // 创建银河系背景
        function createGalaxyBackground() {
            const galaxyGeometry = new THREE.SphereGeometry(500, 32, 32);
            const galaxyMaterial = new THREE.ShaderMaterial({
                vertexShader: `
                    varying vec2 vUv;
                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    varying vec2 vUv;
                    void main() {
                        vec2 center = vec2(0.5, 0.5);
                        float dist = distance(vUv, center);
                        float intensity = 1.0 - smoothstep(0.0, 0.7, dist);
                        vec3 color = mix(vec3(0.1, 0.0, 0.2), vec3(0.3, 0.1, 0.4), intensity);
                        gl_FragColor = vec4(color, intensity * 0.3);
                    }
                `,
                side: THREE.BackSide,
                transparent: true
            });

            const galaxy = new THREE.Mesh(galaxyGeometry, galaxyMaterial);
            scene.add(galaxy);
        }
        
        // 创建地球
        function createEarth() {
            // 地球几何体
            const earthGeometry = new THREE.SphereGeometry(1, 64, 64);

            // 使用更真实的地球纹理URL（备用方案）
            const earthTextureUrls = [
                'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg',
                'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg',
                // 备用纹理 - 如果上面的不可用，使用简单的蓝绿色
                'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=='
            ];

            // 加载地球纹理（带错误处理）
            const earthTexture = textureLoader.load(
                earthTextureUrls[0],
                () => console.log('地球纹理加载完成'),
                undefined,
                (error) => {
                    console.warn('主纹理加载失败，尝试备用纹理');
                    earthTexture.image = createFallbackTexture();
                    earthTexture.needsUpdate = true;
                }
            );

            // 创建备用纹理
            function createFallbackTexture() {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 256;
                const ctx = canvas.getContext('2d');

                // 绘制简单的地球纹理
                const gradient = ctx.createLinearGradient(0, 0, 512, 256);
                gradient.addColorStop(0, '#4A90E2');
                gradient.addColorStop(0.3, '#7ED321');
                gradient.addColorStop(0.7, '#F5A623');
                gradient.addColorStop(1, '#4A90E2');

                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 512, 256);

                // 添加一些大陆形状
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.ellipse(128, 128, 60, 40, 0, 0, Math.PI * 2);
                ctx.fill();

                ctx.beginPath();
                ctx.ellipse(384, 100, 80, 50, 0, 0, Math.PI * 2);
                ctx.fill();

                return canvas;
            }

            // 地球材质
            const earthMaterial = new THREE.MeshPhongMaterial({
                map: earthTexture,
                shininess: 100,
                transparent: false
            });

            // 创建地球网格
            earth = new THREE.Mesh(earthGeometry, earthMaterial);
            earth.castShadow = true;
            earth.receiveShadow = true;

            // 创建地球组
            earthGroup = new THREE.Group();
            earthGroup.add(earth);
            scene.add(earthGroup);

            // 添加夜晚灯光纹理（可选）
            createNightLights();
        }

        // 创建夜晚城市灯光效果
        function createNightLights() {
            const nightGeometry = new THREE.SphereGeometry(1.001, 64, 64);

            // 创建夜晚灯光纹理
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');

            // 黑色背景
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, 512, 256);

            // 添加一些城市灯光点
            ctx.fillStyle = '#FFFF88';
            for (let i = 0; i < 100; i++) {
                const x = Math.random() * 512;
                const y = Math.random() * 256;
                const size = Math.random() * 2 + 1;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }

            const nightTexture = new THREE.CanvasTexture(canvas);

            const nightMaterial = new THREE.MeshBasicMaterial({
                map: nightTexture,
                transparent: true,
                opacity: 0,
                blending: THREE.AdditiveBlending
            });

            const nightLights = new THREE.Mesh(nightGeometry, nightMaterial);
            earthGroup.add(nightLights);

            // 存储夜晚材质以便后续控制
            earth.nightMaterial = nightMaterial;
        }

        // 创建月亮
        function createMoon() {
            // 月亮几何体（相对地球较小）
            const moonGeometry = new THREE.SphereGeometry(0.27, 32, 32); // 月亮直径约为地球的1/4

            // 创建月亮纹理
            function createMoonTexture() {
                const canvas = document.createElement('canvas');
                canvas.width = 256;
                canvas.height = 128;
                const ctx = canvas.getContext('2d');

                // 月亮基础颜色（灰白色）
                const gradient = ctx.createRadialGradient(128, 64, 0, 128, 64, 128);
                gradient.addColorStop(0, '#E8E8E8');
                gradient.addColorStop(0.7, '#C0C0C0');
                gradient.addColorStop(1, '#A0A0A0');

                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 256, 128);

                // 添加月球表面的陨石坑
                ctx.fillStyle = 'rgba(160, 160, 160, 0.6)';
                for (let i = 0; i < 30; i++) {
                    const x = Math.random() * 256;
                    const y = Math.random() * 128;
                    const radius = Math.random() * 8 + 2;

                    ctx.beginPath();
                    ctx.arc(x, y, radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 添加陨石坑的阴影效果
                    ctx.fillStyle = 'rgba(120, 120, 120, 0.4)';
                    ctx.beginPath();
                    ctx.arc(x + radius * 0.3, y + radius * 0.3, radius * 0.7, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillStyle = 'rgba(160, 160, 160, 0.6)';
                }

                return canvas;
            }

            // 尝试加载真实月亮纹理，失败则使用程序化纹理
            const moonTexture = textureLoader.load(
                'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/moon_1024.jpg',
                () => console.log('月亮纹理加载完成'),
                undefined,
                (error) => {
                    console.warn('月亮纹理加载失败，使用程序化纹理');
                    const fallbackTexture = new THREE.CanvasTexture(createMoonTexture());
                    moonMaterial.map = fallbackTexture;
                    moonMaterial.needsUpdate = true;
                }
            );

            // 月亮材质
            const moonMaterial = new THREE.MeshPhongMaterial({
                map: moonTexture,
                shininess: 5, // 月亮表面不太反光
                transparent: false,
                // 月亮表面比较暗，反射率较低
                color: 0xcccccc
            });

            // 创建月亮网格
            moon = new THREE.Mesh(moonGeometry, moonMaterial);
            moon.castShadow = true;
            moon.receiveShadow = true;

            // 创建月亮组（用于轨道运动）
            moonGroup = new THREE.Group();
            moonGroup.add(moon);

            // 设置月亮初始位置（距离地球约3.8个地球半径，真实比例缩放）
            moon.position.set(3.8, 0, 0);

            scene.add(moonGroup);

            // 创建月亮轨道路径（可选显示）
            createMoonOrbit();

            console.log('月亮创建完成');
        }

        // 创建月亮轨道路径
        function createMoonOrbit() {
            const orbitPoints = [];
            const orbitRadius = 3.8;
            const orbitEccentricity = 0.0549;

            // 生成椭圆轨道点
            for (let i = 0; i <= 64; i++) {
                const angle = (i / 64) * Math.PI * 2;
                const r = orbitRadius * (1 - orbitEccentricity * orbitEccentricity) /
                         (1 + orbitEccentricity * Math.cos(angle));

                const x = r * Math.cos(angle);
                const z = r * Math.sin(angle);
                const y = Math.sin(angle * 0.1) * 0.2; // 轻微倾斜

                orbitPoints.push(new THREE.Vector3(x, y, z));
            }

            const orbitGeometry = new THREE.BufferGeometry().setFromPoints(orbitPoints);
            const orbitMaterial = new THREE.LineBasicMaterial({
                color: 0x888888,
                transparent: true,
                opacity: 0.3
            });

            const orbitLine = new THREE.Line(orbitGeometry, orbitMaterial);
            orbitLine.visible = false; // 默认隐藏
            scene.add(orbitLine);

            // 存储轨道线以便控制
            window.moonOrbitLine = orbitLine;
        }
        
        // 创建云层
        function createClouds() {
            const cloudGeometry = new THREE.SphereGeometry(1.01, 64, 64);

            // 创建程序化云层纹理
            function createCloudTexture() {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 256;
                const ctx = canvas.getContext('2d');

                // 透明背景
                ctx.clearRect(0, 0, 512, 256);

                // 创建云层效果
                for (let i = 0; i < 50; i++) {
                    const x = Math.random() * 512;
                    const y = Math.random() * 256;
                    const radius = Math.random() * 30 + 10;
                    const opacity = Math.random() * 0.3 + 0.1;

                    const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
                    gradient.addColorStop(0, `rgba(255, 255, 255, ${opacity})`);
                    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(x, y, radius, 0, Math.PI * 2);
                    ctx.fill();
                }

                return canvas;
            }

            // 尝试加载真实云层纹理，失败则使用程序化纹理
            const cloudTexture = textureLoader.load(
                'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_clouds_1024.png',
                () => console.log('云层纹理加载完成'),
                undefined,
                (error) => {
                    console.warn('云层纹理加载失败，使用程序化纹理');
                    const fallbackTexture = new THREE.CanvasTexture(createCloudTexture());
                    cloudMaterial.map = fallbackTexture;
                    cloudMaterial.needsUpdate = true;
                }
            );

            const cloudMaterial = new THREE.MeshPhongMaterial({
                map: cloudTexture,
                transparent: true,
                opacity: 0.4,
                depthWrite: false
            });

            clouds = new THREE.Mesh(cloudGeometry, cloudMaterial);

            cloudGroup = new THREE.Group();
            cloudGroup.add(clouds);
            scene.add(cloudGroup);
        }
        
        // 创建大气层
        function createAtmosphere() {
            const atmosphereGeometry = new THREE.SphereGeometry(1.15, 64, 64);

            const atmosphereMaterial = new THREE.ShaderMaterial({
                vertexShader: `
                    varying vec3 vNormal;
                    varying vec3 vPosition;
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vPosition = position;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    varying vec3 vNormal;
                    varying vec3 vPosition;

                    void main() {
                        vec3 viewDirection = normalize(vPosition - cameraPosition);
                        float intensity = pow(0.7 - dot(vNormal, viewDirection), 2.0);

                        // 添加一些动态效果
                        float pulse = sin(time * 0.5) * 0.1 + 0.9;
                        intensity *= pulse;

                        // 大气层颜色渐变
                        vec3 atmosphereColor = mix(
                            vec3(0.3, 0.6, 1.0), // 蓝色
                            vec3(0.8, 0.4, 0.2), // 橙色（日落效果）
                            intensity * 0.3
                        );

                        gl_FragColor = vec4(atmosphereColor, intensity * 0.6);
                    }
                `,
                uniforms: {
                    time: { value: 0.0 }
                },
                blending: THREE.AdditiveBlending,
                side: THREE.BackSide,
                transparent: true
            });

            atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
            atmosphere.material.uniforms.time = { value: 0.0 };
            scene.add(atmosphere);
        }
        
        // 创建光照
        function createLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);
            
            // 方向光（模拟太阳光）
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 3, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            const canvas = document.getElementById('earthCanvas');
            
            // 鼠标事件
            canvas.addEventListener('mousedown', onMouseDown);
            canvas.addEventListener('mousemove', onMouseMove);
            canvas.addEventListener('mouseup', onMouseUp);
            canvas.addEventListener('wheel', onMouseWheel);
            
            // 控制面板事件
            document.getElementById('rotationSpeed').addEventListener('input', (e) => {
                rotationSpeed = parseFloat(e.target.value) * 0.01;
            });
            
            document.getElementById('zoomLevel').addEventListener('input', (e) => {
                camera.position.z = parseFloat(e.target.value);
            });
            
            document.getElementById('showClouds').addEventListener('change', (e) => {
                cloudGroup.visible = e.target.checked;
            });
            
            document.getElementById('showAtmosphere').addEventListener('change', (e) => {
                atmosphere.visible = e.target.checked;
            });
            
            document.getElementById('nightMode').addEventListener('change', (e) => {
                const isNightMode = e.target.checked;

                // 调整光照
                scene.children.forEach(child => {
                    if (child.type === 'DirectionalLight') {
                        child.intensity = isNightMode ? 0.1 : 1;
                    } else if (child.type === 'AmbientLight') {
                        child.intensity = isNightMode ? 0.1 : 0.3;
                    }
                });

                // 切换夜晚灯光效果
                if (earth && earth.nightMaterial) {
                    earth.nightMaterial.opacity = isNightMode ? 0.8 : 0;
                }

                // 调整大气层效果
                if (atmosphere) {
                    const material = atmosphere.material;
                    if (isNightMode) {
                        material.uniforms.time.value = -1; // 特殊标记夜晚模式
                    }
                }
            });

            // 月亮控制
            document.getElementById('showMoon').addEventListener('change', (e) => {
                if (moonGroup) {
                    moonGroup.visible = e.target.checked;
                }
            });

            document.getElementById('moonOrbitSpeed').addEventListener('input', (e) => {
                // 月亮轨道速度将在动画循环中使用
                window.moonOrbitSpeed = parseFloat(e.target.value);
            });

            document.getElementById('showOrbit').addEventListener('change', (e) => {
                if (window.moonOrbitLine) {
                    window.moonOrbitLine.visible = e.target.checked;
                }
            });
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);
        }
        
        // 鼠标事件处理
        function onMouseDown(event) {
            isMouseDown = true;
            mouse.x = event.clientX;
            mouse.y = event.clientY;
            autoRotate = false;
        }
        
        function onMouseMove(event) {
            if (!isMouseDown) return;

            const deltaX = event.clientX - mouse.x;
            const deltaY = event.clientY - mouse.y;

            earthGroup.rotation.y += deltaX * 0.01;
            earthGroup.rotation.x += deltaY * 0.01;

            if (cloudGroup) {
                cloudGroup.rotation.y += deltaX * 0.01;
                cloudGroup.rotation.x += deltaY * 0.01;
            }

            // 月亮轨道也跟随地球旋转
            if (moonGroup) {
                moonGroup.rotation.y += deltaX * 0.01;
                moonGroup.rotation.x += deltaY * 0.01;
            }

            mouse.x = event.clientX;
            mouse.y = event.clientY;
        }
        
        function onMouseUp() {
            isMouseDown = false;
            setTimeout(() => { autoRotate = true; }, 2000);
        }
        
        function onMouseWheel(event) {
            event.preventDefault();
            camera.position.z += event.deltaY * 0.01;
            camera.position.z = Math.max(2, Math.min(10, camera.position.z));
            document.getElementById('zoomLevel').value = camera.position.z;
        }
        
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            const time = Date.now() * 0.001;

            // 性能监控
            updateFPS();

            // 自动旋转
            if (autoRotate && rotationSpeed > 0) {
                earthGroup.rotation.y += rotationSpeed;
                if (cloudGroup) {
                    cloudGroup.rotation.y += rotationSpeed * 1.1; // 云层稍快一点
                    cloudGroup.rotation.x += Math.sin(time * 0.1) * 0.001; // 轻微摆动
                }
            }

            // 月亮轨道运动
            if (moonGroup && moon) {
                const orbitSpeed = (window.moonOrbitSpeed || 1) * 0.02;
                moonOrbitAngle += orbitSpeed;

                // 月亮围绕地球的椭圆轨道
                const orbitRadius = 3.8;
                const orbitEccentricity = 0.0549; // 月球轨道的真实离心率

                // 计算椭圆轨道位置
                const a = orbitRadius; // 半长轴
                const e = orbitEccentricity;
                const r = a * (1 - e * e) / (1 + e * Math.cos(moonOrbitAngle));

                moon.position.x = r * Math.cos(moonOrbitAngle);
                moon.position.z = r * Math.sin(moonOrbitAngle);
                moon.position.y = Math.sin(moonOrbitAngle * 0.1) * 0.2; // 轻微的轨道倾斜

                // 月亮自转（潮汐锁定，始终同一面朝向地球）
                moon.rotation.y = -moonOrbitAngle;

                // 月亮相位效果（根据太阳光照角度调整亮度）
                if (moon.material) {
                    // 计算月亮相对于太阳光源的角度
                    const sunDirection = new THREE.Vector3(5, 3, 5).normalize(); // 太阳光方向
                    const moonDirection = new THREE.Vector3(moon.position.x, moon.position.y, moon.position.z).normalize();

                    // 计算月相（0-1，0为新月，0.5为满月）
                    const dotProduct = sunDirection.dot(moonDirection);
                    const phase = (dotProduct + 1) / 2;

                    // 根据月相调整月亮亮度
                    const brightness = 0.6 + phase * 0.4; // 基础亮度 + 相位亮度
                    moon.material.color.setScalar(brightness);
                }
            }

            // 更新大气层动画
            if (atmosphere && atmosphere.material.uniforms) {
                atmosphere.material.uniforms.time.value = time;
            }

            // 添加轻微的地球摆动效果（仅在高性能设备上）
            if (earthGroup && fps > 45) {
                earthGroup.rotation.z = Math.sin(time * 0.2) * 0.02;
            }

            // 相机轻微摆动（增加动态感，仅在高性能设备上）
            if (fps > 45) {
                camera.position.x = Math.sin(time * 0.1) * 0.1;
                camera.position.y = Math.cos(time * 0.15) * 0.05;
                camera.lookAt(0, 0, 0);
            }

            renderer.render(scene, camera);
        }
        
        // 性能监控
        let frameCount = 0;
        let lastFPSTime = Date.now();
        let fps = 60;

        function updateFPS() {
            frameCount++;
            const now = Date.now();
            if (now - lastFPSTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (now - lastFPSTime));
                frameCount = 0;
                lastFPSTime = now;

                // 根据FPS调整质量
                if (fps < 30 && renderer.getPixelRatio() > 1) {
                    renderer.setPixelRatio(1);
                    console.log('降低渲染质量以提升性能');
                }
            }
        }

        // 检查WebGL支持
        function checkWebGLSupport() {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) {
                throw new Error('您的浏览器不支持WebGL');
            }
            return true;
        }

        // 初始化应用
        async function init() {
            try {
                // 检查WebGL支持
                checkWebGLSupport();

                // 更新加载状态
                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>初始化3D场景...</div>';

                initScene();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>创建地球模型...</div>';
                createEarth();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>添加云层效果...</div>';
                createClouds();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>生成大气层...</div>';
                createAtmosphere();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>创建月亮...</div>';
                createMoon();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>设置光照系统...</div>';
                createLighting();

                loadingElement.innerHTML = '<div class="loading-spinner"></div><div>配置交互控制...</div>';
                setupEventListeners();

                // 等待一小段时间确保所有资源加载完成
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 隐藏加载界面
                loadingElement.style.opacity = '0';
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                }, 500);

                // 开始动画循环
                animate();

                console.log('3D地球模型初始化完成');

            } catch (error) {
                console.error('初始化失败:', error);
                loadingElement.innerHTML = `
                    <div style="color: red; text-align: center;">
                        <h3>加载失败</h3>
                        <p>${error.message}</p>
                        <p>请检查您的浏览器是否支持WebGL，然后刷新页面重试</p>
                        <button onclick="location.reload()" style="
                            background: #ff4444;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin-top: 10px;
                        ">刷新页面</button>
                    </div>
                `;
            }
        }

        // 启动应用
        window.addEventListener('load', init);

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (renderer) {
                renderer.dispose();
            }
            if (scene) {
                scene.traverse((object) => {
                    if (object.geometry) object.geometry.dispose();
                    if (object.material) {
                        if (Array.isArray(object.material)) {
                            object.material.forEach(material => material.dispose());
                        } else {
                            object.material.dispose();
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
