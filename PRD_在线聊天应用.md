# 在线聊天应用产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品愿景
构建一个简洁、高效的在线聊天应用，为用户提供流畅的即时通讯体验，支持好友管理和实时聊天功能。

### 1.2 产品目标
- 提供稳定可靠的即时通讯服务
- 简化用户交互流程，提升用户体验
- 建立完善的好友关系管理系统
- 实现跨平台的实时消息传递

### 1.3 目标用户
- 主要用户：需要日常沟通的个人用户（18-45岁）
- 使用场景：工作协作、朋友聊天、社交互动

## 2. 功能需求

### 2.1 用户认证系统

#### 2.1.1 用户注册
**功能描述**：新用户使用用户名和密码创建账户

**具体需求**：
- 用户名：3-20字符，支持字母、数字、下划线
- 密码：最少8位，包含字母和数字
- 确认密码：与密码保持一致
- 用户名唯一性检查
- 密码强度验证
- 注册成功后自动登录

**验收标准**：
- 表单验证准确
- 重复用户名提示
- 密码强度实时检测
- 注册流程在15秒内完成

#### 2.1.2 用户登录
**功能描述**：已注册用户使用用户名和密码登录

**具体需求**：
- 使用用户名登录
- 密码错误提示
- "记住我"功能（7天免登录）
- 登录失败次数限制（5次后锁定30分钟）
- 登录状态保持

**验收标准**：
- 登录响应时间<2秒
- 错误信息准确友好
- 安全登录验证
- 多设备登录管理

### 2.2 好友管理系统

#### 2.2.1 好友列表
**功能描述**：查看和管理已添加的好友

**具体需求**：
- 显示好友头像、用户名、在线状态
- 支持按字母排序
- 好友搜索功能
- 好友分组管理（可选）
- 好友信息查看

**验收标准**：
- 列表加载速度<3秒
- 在线状态实时更新
- 搜索结果准确
- 支持100+好友展示

#### 2.2.2 发送好友请求
**功能描述**：向其他用户发送好友申请

**具体需求**：
- 通过用户名搜索用户
- 发送好友请求附带消息
- 防止重复发送请求
- 显示请求发送状态
- 搜索结果展示用户基本信息

**验收标准**：
- 用户搜索准确快速
- 请求发送成功率>99%
- 重复请求拦截有效
- 搜索支持模糊匹配

#### 2.2.3 好友请求管理
**功能描述**：查看和处理收到的及发出的好友请求

**具体需求**：
- **接收的请求**：
  - 显示请求者信息和消息
  - 接受/拒绝操作
  - 请求时间显示
  - 未处理请求数量提醒
  
- **发起的请求**：
  - 查看已发送的请求状态
  - 撤销未处理的请求
  - 请求状态：待处理/已接受/已拒绝

**验收标准**：
- 请求列表实时更新
- 操作响应时间<1秒
- 状态变更及时通知
- 历史记录完整保存

### 2.3 私聊功能

#### 2.3.1 聊天列表
**功能描述**：显示所有聊天会话的概览

**具体需求**：
- 按最近消息时间排序
- 显示最后一条消息预览
- 未读消息数量提醒
- 好友头像和在线状态
- 会话置顶功能
- **删除聊天功能**：
  - 用户可以删除聊天会话
  - 删除后仅对删除方不可见，对方仍可正常查看
  - 删除方重新收到该好友消息时，会话重新出现在聊天列表
- **会话可见性规则**：
  - 对于发起方：创建会话后立即在列表中显示
  - 对于接收方：只有收到第一条消息后才在列表中显示该会话

**验收标准**：
- 列表加载<2秒
- 未读消息实时更新
- 消息预览准确截断
- 支持50+并发聊天
- 会话可见性逻辑准确
- 删除聊天功能正确执行（仅删除方不可见）

#### 2.3.2 发起私聊
**功能描述**：与好友开始新的聊天对话

**具体需求**：
- 从好友列表选择聊天对象
- 自动创建聊天会话
- 进入聊天界面
- 支持发送文字消息
- 消息发送状态显示
- 消息时间戳
- 聊天记录本地存储（IndexedDB）
- 实时消息传输（WebSocket）
- **重要规则**：只有发起方发送第一条消息后，接收方才能在聊天列表中看到该会话

**验收标准**：
- 消息发送成功率>99%
- WebSocket消息延迟<1秒
- 聊天界面响应流畅
- IndexedDB历史记录可靠保存
- 离线模式下可查看历史消息
- 会话可见性规则正确执行

### 2.4 群聊功能

#### 2.4.1 创建群聊
**功能描述**：用户可以创建群聊并邀请好友加入

**具体需求**：
- 创建群聊时设置群名称
- 从好友列表选择群成员（多选）
- 自动拉取选中的好友进入群聊，无需同意
- 创建者自动成为群管理员
- 系统自动发送欢迎消息："欢迎 [用户名] 加入群聊"
- 群聊立即在所有成员的聊天列表中显示

**验收标准**：
- 群聊创建成功率>99%
- 成员自动加入无需确认
- 系统消息正确生成
- 群聊在所有成员列表中可见
- 创建者权限正确设置

#### 2.4.2 群聊管理
**功能描述**：群管理员可以管理群聊设置和成员

**具体需求**：
- **群管理员权限**：
  - 修改群名称
  - 邀请新成员加入（自动拉入，无需同意）
  - 移除群成员
  - 解散群聊
- **成员权限**：
  - 查看群成员列表
  - 发送群消息
  - 退出群聊
- **系统消息自动触发**：
  - 新成员加入："欢迎 [用户名] 加入群聊"
  - 成员退出："[用户名] 退出了群聊"
  - 成员被移除："[用户名] 被移出群聊"
  - 群名称修改："群名称已修改为 [新名称]"

**验收标准**：
- 权限控制准确执行
- 系统消息及时生成
- 成员操作实时同步
- 群解散后所有成员收到通知

#### 2.4.3 群聊消息
**功能描述**：群成员之间的多人实时聊天

**具体需求**：
- 支持群内文字消息发送
- 显示发送者名称和头像
- 消息时间戳显示
- @功能（可选）
- 消息已读状态（显示已读人数）
- 系统消息与用户消息区分显示

**验收标准**：
- 群消息发送成功率>99%
- 所有成员实时接收消息
- 发送者信息正确显示
- 系统消息样式区分明显

#### 2.4.4 退出与解散群聊
**功能描述**：成员可退出群聊，管理员可解散群聊

**具体需求**：
- **普通成员**：
  - 可主动退出群聊
  - 退出后群聊从聊天列表中消失
  - 系统自动发送退出消息给其他成员
- **群管理员**：
  - 可选择退出群聊（需转让管理员权限或解散群聊）
  - 可直接解散群聊
  - 解散后所有成员收到解散通知
  - 群聊从所有成员的聊天列表中消失

**验收标准**：
- 退出操作成功执行
- 系统消息正确发送
- 权限转移机制正常
- 解散通知及时到达所有成员

## 3. 用户体验流程

### 3.1 新用户注册流程
1. 访问应用 → 点击注册
2. 填写用户名、密码、确认密码
3. 提交注册 → 系统验证
4. 验证成功 → 自动登录
5. 引导完善个人信息

### 3.2 好友添加流程
1. 进入好友管理 → 点击添加好友
2. 搜索用户名 → 选择目标用户
3. 发送好友请求（可添加消息）
4. 等待对方确认
5. 好友关系建立 → 可开始聊天

### 3.3 聊天交互流程
**发起方流程**：
1. 从好友列表选择好友 → 点击聊天
2. 创建聊天会话 → 会话立即显示在自己的聊天列表
3. 输入并发送第一条消息
4. 消息发送成功后，接收方聊天列表中显示该会话
5. 继续正常聊天交互

**接收方流程**：
1. 收到好友发送的第一条消息
2. 会话自动出现在聊天列表中
3. 显示未读消息提醒
4. 点击进入聊天界面回复

### 3.4 删除私聊流程
**删除操作**：
1. 在聊天列表中长按或右键点击私聊会话
2. 选择"删除聊天"选项
3. 确认删除 → 会话从当前用户的聊天列表中消失
4. 对方的聊天列表不受影响，仍可正常查看和发送消息

**恢复机制**：
1. 对方向已删除会话的用户发送新消息
2. 系统自动恢复删除方的会话可见性
3. 会话重新出现在删除方的聊天列表中
4. 显示未读消息提醒

### 3.5 群聊操作流程

#### 3.5.1 创建群聊流程
1. 点击"创建群聊"按钮
2. 输入群聊名称
3. 从好友列表中选择群成员（多选）
4. 点击"创建"确认
5. 系统自动：
   - 创建群聊，设置创建者为管理员
   - 自动拉取所有选中好友进群（无需同意）
   - 在所有成员的聊天列表中显示群聊
   - 发送系统欢迎消息："欢迎 [用户名] 加入群聊"（每个成员一条）

#### 3.5.2 群聊管理流程
**管理员操作**：
1. 进入群聊设置界面
2. 可进行的操作：
   - 修改群名称 → 系统发送名称变更消息
   - 邀请新成员 → 从好友列表选择 → 自动拉入 → 发送欢迎消息
   - 移除成员 → 选择成员 → 确认移除 → 发送移除消息
   - 解散群聊 → 确认解散 → 所有成员收到解散通知

**普通成员操作**：
1. 查看群成员列表
2. 发送群消息
3. 退出群聊 → 确认退出 → 系统发送退出消息给其他成员

#### 3.5.3 群聊消息流程
1. 进入群聊界面
2. 输入消息内容
3. 发送消息 → 所有群成员实时接收
4. 消息显示：发送者头像、用户名、消息内容、时间戳
5. 系统消息以特殊样式显示，与普通消息区分

## 4. 技术要求

### 4.1 前端技术栈
- React.js + TypeScript
- 状态管理：Redux/Zustand
- UI框架：Ant Design/Material-UI
- **WebSocket客户端**：用于实时消息传输
- **IndexedDB**：本地存储聊天记录和离线数据
- 响应式设计支持移动端

### 4.2 后端技术要求
- Node.js/Python/Java后端API
- **WebSocket服务器**：处理实时消息推送和用户在线状态
- 数据库：MySQL/PostgreSQL（存储用户信息、好友关系、消息备份）
- Redis缓存用户在线状态和WebSocket连接
- JWT身份验证

### 4.3 非功能性需求
- **性能**：消息延迟<1秒，页面加载<3秒
- **可用性**：系统可用率>99.5%
- **扩展性**：支持10,000+并发WebSocket连接
- **安全性**：密码加密、SQL注入防护、WebSocket安全认证
- **兼容性**：主流浏览器，移动端适配
- **离线支持**：IndexedDB支持离线查看历史消息

### 4.4 数据存储策略
**本地存储 (IndexedDB)**：
- 聊天消息记录（文本、时间戳、发送状态）
- 好友列表缓存
- 用户会话信息（包括删除状态）
- 离线消息队列
- 本地数据与服务器数据同步机制
- 用户删除的会话列表（用于过滤显示）

**服务器存储**：
- 用户账户信息
- 好友关系数据
- 消息备份（用于多设备同步）
- 系统日志和统计数据

## 5. 数据模型设计

### 5.1 用户表 (Users)
```sql
- id: 主键
- username: 用户名（唯一）
- password_hash: 密码哈希
- avatar_url: 头像链接
- is_online: 在线状态
- last_seen: 最后上线时间
- created_at: 创建时间
- updated_at: 更新时间
```

### 5.2 好友关系表 (Friendships)
```sql
- id: 主键
- user_id: 用户ID
- friend_id: 好友ID
- status: 状态（pending/accepted/rejected）
- created_at: 创建时间
- updated_at: 更新时间
```

### 5.3 聊天会话表 (Conversations)
```sql
- id: 主键
- user1_id: 用户1 ID
- user2_id: 用户2 ID
- created_by: 创建者ID（发起方）
- last_message_id: 最后一条消息ID
- user1_visible: 用户1是否可见（默认：创建者为true）
- user2_visible: 用户2是否可见（默认：非创建者为false，收到第一条消息后变为true）
- user1_deleted: 用户1是否已删除此会话（默认：false）
- user2_deleted: 用户2是否已删除此会话（默认：false）
- created_at: 创建时间
- updated_at: 更新时间
```

### 5.4 群聊表 (Groups)
```sql
- id: 主键
- group_name: 群名称
- creator_id: 创建者ID（群管理员）
- member_count: 群成员数量
- created_at: 创建时间
- updated_at: 更新时间
- is_active: 群聊状态（true: 活跃, false: 已解散）
```

### 5.5 群成员表 (GroupMembers)
```sql
- id: 主键
- group_id: 群聊ID
- user_id: 用户ID
- role: 角色（admin: 管理员, member: 普通成员）
- joined_at: 加入时间
- is_active: 成员状态（true: 在群, false: 已退出）
```

### 5.6 聊天消息表 (Messages)
```sql
- id: 主键
- conversation_id: 私聊会话ID（nullable）
- group_id: 群聊ID（nullable）
- sender_id: 发送者ID
- content: 消息内容
- message_type: 消息类型（text/image/file/system）
- system_message_type: 系统消息类型（welcome/leave/remove/rename等，仅system消息有效）
- is_read: 是否已读（私聊使用）
- is_first_message: 是否为会话的第一条消息（私聊使用）
- created_at: 发送时间
```

### 5.7 群消息已读表 (GroupMessageReads)
```sql
- id: 主键
- message_id: 消息ID
- user_id: 用户ID
- group_id: 群聊ID
- read_at: 已读时间
```

## 6. API接口规范

### 6.1 认证接口
```
POST /api/auth/register - 用户注册
POST /api/auth/login - 用户登录
POST /api/auth/logout - 用户登出
PUT /api/auth/change-password - 修改密码
```

### 6.2 好友管理接口
```
GET /api/friends - 获取好友列表
POST /api/friends/request - 发送好友请求
GET /api/friends/requests - 获取好友请求列表
PUT /api/friends/requests/:id - 处理好友请求
DELETE /api/friends/:id - 删除好友
```

### 6.3 私聊接口
**REST API**：
```
GET /api/conversations - 获取用户可见且未删除的私聊列表
POST /api/conversations - 创建私聊会话（仅发起方可见）
GET /api/conversations/:id/messages - 获取私聊记录（用于同步到IndexedDB）
PUT /api/messages/:id/read - 标记私聊消息已读
PUT /api/conversations/:id/visibility - 更新会话可见性（系统内部使用）
DELETE /api/conversations/:id - 删除私聊会话（仅对当前用户不可见）
PUT /api/conversations/:id/restore - 恢复已删除的会话（收到新消息时自动触发）
```

### 6.4 群聊接口
**REST API**：
```
POST /api/groups - 创建群聊
GET /api/groups - 获取用户参与的群聊列表
GET /api/groups/:id - 获取群聊详细信息
PUT /api/groups/:id - 修改群聊信息（仅管理员）
DELETE /api/groups/:id - 解散群聊（仅管理员）

POST /api/groups/:id/members - 邀请新成员加入群聊（仅管理员）
GET /api/groups/:id/members - 获取群成员列表
DELETE /api/groups/:id/members/:userId - 移除群成员（仅管理员）
POST /api/groups/:id/leave - 退出群聊

GET /api/groups/:id/messages - 获取群聊消息记录
POST /api/groups/:id/messages - 发送群消息
PUT /api/groups/:id/messages/:messageId/read - 标记群消息已读
```

**WebSocket事件**：
```
私聊消息相关:
  发送私聊消息: send_private_message
  接收私聊消息: receive_private_message
  私聊消息状态更新: private_message_status_update
  会话可见性变更: conversation_visibility_change

群聊消息相关:
  发送群消息: send_group_message
  接收群消息: receive_group_message
  群系统消息: group_system_message
  群成员变更: group_member_change
  群信息更新: group_info_update

用户状态相关:
  用户上线: user_online
  用户下线: user_offline
```

## 7. 成功指标

### 7.1 用户参与度
- 日活跃用户数 (DAU)
- 用户留存率（7天/30天）
- 平均会话时长
- 消息发送频率

### 7.2 功能效果
- 注册转化率 >80%
- 好友请求接受率 >70%
- 消息发送成功率 >99%
- 用户满意度评分 >4.0/5.0

### 7.3 技术指标
- 系统响应时间 <2秒
- WebSocket消息传递延迟 <1秒
- 系统可用性 >99.5%
- 错误率 <1%
- IndexedDB读写性能 <100ms
- 离线消息同步成功率 >95%

## 8. 用户故事

### 8.1 用户注册登录
```
作为新用户，我希望能够快速注册账号，这样我就可以开始使用聊天功能
作为老用户，我希望能够快速登录，这样我就可以立即查看未读消息
```

### 8.2 好友管理
```
作为用户，我希望能够搜索并添加好友，这样我就可以扩展社交圈
作为用户，我希望能够管理好友请求，这样我就可以控制谁能联系我
作为用户，我希望看到好友的在线状态，这样我就知道何时联系他们
```

### 8.3 聊天交流
```
作为用户，我希望能够与好友实时聊天，这样我们就可以及时沟通
作为用户，我希望能够查看聊天历史，这样我就可以回顾重要信息
作为用户，我希望知道消息是否被对方看到，这样我就能确认沟通效果
作为发起聊天的用户，我希望创建会话后能立即在我的聊天列表中看到
作为接收消息的用户，我希望只有在收到第一条消息时才看到新会话，避免骚扰
作为用户，我希望能够删除不需要的聊天会话，让我的聊天列表更整洁
作为用户，我希望删除聊天后对方仍能正常使用，这样不会影响对方的体验
作为用户，我希望删除的聊天在收到新消息时能重新出现，这样不会错过重要信息
```

## 9. 实施计划

### Phase 1: 基础功能 (4周)
- 用户注册登录系统
- 基础UI框架搭建
- 数据库设计实现

### Phase 2: 好友系统 (3周)
- 好友添加功能
- 好友请求管理
- 好友列表展示

### Phase 3: 聊天功能 (4周)
- 实时消息传递
- 聊天界面开发
- 消息历史管理

### Phase 4: 优化完善 (2周)
- 性能优化
- Bug修复
- 用户体验优化

## 10. 风险评估

### 10.1 技术风险
- WebSocket连接稳定性
- 高并发消息处理
- 数据一致性保证

### 10.2 产品风险
- 用户习惯培养
- 竞品功能对比
- 用户反馈响应

### 10.3 缓解措施
- 充分的技术调研和测试
- 分阶段发布和用户反馈收集
- 建立完善的监控和告警机制